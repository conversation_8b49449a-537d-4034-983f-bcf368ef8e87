import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { MobileChatParticipants } from '../components/MobileChatParticipants';
import { UniversalChat } from '../UniversalChat';
import { Users, MessageCircle, Smartphone, Monitor } from 'lucide-react';
import { Chat } from '@/types/chat';
import { ChatProvider } from '@/contexts/ChatContext';

/**
 * Example showing mobile-responsive Chat layout fixes
 * Demonstrates the fixed mobile layout that shows either conversation list OR chat content
 */
export const MobileResponsiveExample: React.FC = () => {
  const [showParticipants, setShowParticipants] = useState(false);
  const [showFullChat, setShowFullChat] = useState(false);

  // Mock chat data
  const mockChat: Chat = {
    id: 'chat-123',
    type: 'group',
    name: 'Project Discussion',
    description: 'Discussion about the new mobile app features and implementation details.',
    created_at: '2024-01-15T10:30:00Z',
    participants: [
      {
        id: 'user-1',
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
        type: 'admin',
        is_online: true,
        last_seen: null,
      },
      {
        id: 'user-2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
        type: 'provider',
        is_online: true,
        last_seen: null,
      },
      {
        id: 'user-3',
        name: 'Mike Chen',
        email: '<EMAIL>',
        avatar: null,
        type: 'customer',
        is_online: false,
        last_seen: '2 hours ago',
      },
      {
        id: 'user-4',
        name: 'Emily Davis',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face',
        type: 'provider',
        is_online: false,
        last_seen: '1 day ago',
      },
    ],
  };

  const features = {
    canManageParticipants: true,
    canSeeOnlineStatus: true,
    hasVideoCall: true,
    canCreateGroups: true,
    maxFileSize: 50,
    canAccessHistory: true,
    hasFileUpload: true,
    hasVoiceMessages: true,
    hasTypingIndicators: true,
    canInitiateChat: true,
    canMuteConversations: true,
    canPinMessages: true,
    canReportMessages: true,
    canBlockUsers: true,
    canDeleteMessages: true,
    hasReadReceipts: true,
    canEditMessages: true,
    allowedFileTypes: ['image/*', 'application/pdf'],
    maxMessageLength: 2000,
  };

  const theme = {
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#f59e0b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#1e293b',
    textSecondary: '#64748b',
    border: '#e2e8f0',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  };

  return (
    <ChatProvider>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Header */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h1 className="text-3xl font-bold mb-4 flex items-center gap-3">
              <Smartphone className="h-8 w-8 text-blue-600" />
              Mobile-Responsive Chat Layout
            </h1>
            <p className="text-gray-600 mb-6">
              This example demonstrates the fixed mobile chat layout that properly handles the conversation list and chat content:
            </p>

            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="space-y-3">
                <h3 className="font-semibold text-green-700 flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  Mobile Layout (Fixed)
                </h3>
                <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
                  <li><strong>Single view</strong> - Shows either conversation list OR chat content</li>
                  <li><strong>Back navigation</strong> - Easy return to conversation list</li>
                  <li><strong>Touch-friendly</strong> - 44px minimum touch targets</li>
                  <li><strong>Mobile-optimized</strong> - Larger text and better spacing</li>
                  <li><strong>Drawer participants</strong> - Bottom-up participant panel</li>
                </ul>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold text-blue-700 flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  Desktop Layout
                </h3>
                <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
                  <li><strong>Split view</strong> - Sidebar and chat content side by side</li>
                  <li><strong>Sheet participants</strong> - Right-side participant panel</li>
                  <li><strong>Compact design</strong> - Efficient use of screen space</li>
                  <li><strong>Multi-tasking</strong> - View conversations while chatting</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Demo Chat Interface */}
          <div className="border rounded-lg overflow-hidden">
            {/* Mock Chat Header */}
            <div className="bg-blue-50 border-b p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <MessageCircle className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold">{mockChat.name}</h3>
                  <p className="text-sm text-gray-600">{mockChat.participants.length} participants</p>
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowParticipants(true)}
                className="flex items-center gap-2"
              >
                <Users className="h-4 w-4" />
                <span className="hidden sm:inline">View Participants</span>
                <span className="sm:hidden">({mockChat.participants.length})</span>
              </Button>
            </div>

            {/* Mock Chat Content */}
            <div className="h-64 bg-gray-50 flex items-center justify-center">
              <p className="text-gray-500">Chat messages would appear here...</p>
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">Try it out:</h4>
            <p className="text-blue-800 text-sm">
              Click "View Participants" to see the mobile-responsive participants panel.
              Try resizing your browser or viewing on mobile to see the different layouts.
            </p>
          </div>
        </div>

        {/* Live Chat Demo */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
            <h2 className="text-xl font-semibold mb-2">Live Chat Demo</h2>
            <p className="text-gray-600 text-sm">
              This is the actual UniversalChat component with the mobile layout fixes applied.
              On mobile, you'll see either the conversation list OR the chat content, not both.
            </p>
          </div>

          <div className="h-[600px]">
            <UniversalChat
              userRole="admin"
              userId="demo-admin"
              variant="full"
              theme="admin"
              onChatSelect={(chatId) => {
                console.log('Selected chat:', chatId);
              }}
              onMessageSent={(message) => {
                console.log('Sent message:', message);
              }}
              onError={(error) => {
                console.error('Chat error:', error);
              }}
            />
          </div>
        </div>
      </div>

      {/* Mobile-Responsive Participants Panel */}
      <MobileChatParticipants
        chat={mockChat}
        userRole="admin"
        features={features}
        theme={theme}
        isOpen={showParticipants}
        onClose={() => setShowParticipants(false)}
        onAddParticipant={(userId) => {
          console.log('Add participant:', userId);
        }}
        onRemoveParticipant={(userId) => {
          console.log('Remove participant:', userId);
        }}
        onPromoteParticipant={(userId) => {
          console.log('Promote participant:', userId);
        }}
      />
    </ChatProvider>
  );
};

export default MobileResponsiveExample;
