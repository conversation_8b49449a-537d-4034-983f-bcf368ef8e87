import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { MobileChatParticipants } from '../components/MobileChatParticipants';
import { Users, MessageCircle } from 'lucide-react';
import { Chat } from '@/types/chat';

/**
 * Example showing mobile-responsive ChatParticipants usage
 * Demonstrates best practices for mobile UX/UI
 */
export const MobileResponsiveExample: React.FC = () => {
  const [showParticipants, setShowParticipants] = useState(false);

  // Mock chat data
  const mockChat: Chat = {
    id: 'chat-123',
    type: 'group',
    name: 'Project Discussion',
    description: 'Discussion about the new mobile app features and implementation details.',
    created_at: '2024-01-15T10:30:00Z',
    participants: [
      {
        id: 'user-1',
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
        type: 'admin',
        is_online: true,
        last_seen: null,
      },
      {
        id: 'user-2',
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
        type: 'provider',
        is_online: true,
        last_seen: null,
      },
      {
        id: 'user-3',
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: null,
        type: 'customer',
        is_online: false,
        last_seen: '2 hours ago',
      },
      {
        id: 'user-4',
        name: 'Emily Davis',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face',
        type: 'provider',
        is_online: false,
        last_seen: '1 day ago',
      },
    ],
  };

  const features = {
    canManageParticipants: true,
    canSeeOnlineStatus: true,
    hasVideoCall: true,
    canCreateGroups: true,
    maxFileSize: 50,
  };

  const theme = {
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#f59e0b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#1e293b',
    textSecondary: '#64748b',
    border: '#e2e8f0',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h1 className="text-2xl font-bold mb-4">Mobile-Responsive Chat Participants</h1>
          <p className="text-gray-600 mb-6">
            This example demonstrates the mobile-optimized ChatParticipants component with:
          </p>
          
          <ul className="list-disc list-inside space-y-2 text-sm text-gray-600 mb-8">
            <li><strong>Mobile-first responsive design</strong> - Adapts to different screen sizes</li>
            <li><strong>Touch-friendly targets</strong> - Minimum 44px touch targets on mobile</li>
            <li><strong>Optimized typography</strong> - Larger text and better spacing on mobile</li>
            <li><strong>Smart layout</strong> - Uses Drawer on mobile, Sheet on desktop</li>
            <li><strong>Enhanced empty states</strong> - Better visual feedback</li>
            <li><strong>Improved search UX</strong> - Mobile-friendly input sizing</li>
          </ul>

          {/* Demo Chat Interface */}
          <div className="border rounded-lg overflow-hidden">
            {/* Mock Chat Header */}
            <div className="bg-blue-50 border-b p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <MessageCircle className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold">{mockChat.name}</h3>
                  <p className="text-sm text-gray-600">{mockChat.participants.length} participants</p>
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowParticipants(true)}
                className="flex items-center gap-2"
              >
                <Users className="h-4 w-4" />
                <span className="hidden sm:inline">View Participants</span>
                <span className="sm:hidden">({mockChat.participants.length})</span>
              </Button>
            </div>

            {/* Mock Chat Content */}
            <div className="h-64 bg-gray-50 flex items-center justify-center">
              <p className="text-gray-500">Chat messages would appear here...</p>
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">Try it out:</h4>
            <p className="text-blue-800 text-sm">
              Click "View Participants" to see the mobile-responsive participants panel. 
              Try resizing your browser or viewing on mobile to see the different layouts.
            </p>
          </div>
        </div>
      </div>

      {/* Mobile-Responsive Participants Panel */}
      <MobileChatParticipants
        chat={mockChat}
        userRole="admin"
        features={features}
        theme={theme}
        isOpen={showParticipants}
        onClose={() => setShowParticipants(false)}
        onAddParticipant={(userId) => {
          console.log('Add participant:', userId);
        }}
        onRemoveParticipant={(userId) => {
          console.log('Remove participant:', userId);
        }}
        onPromoteParticipant={(userId) => {
          console.log('Promote participant:', userId);
        }}
      />
    </div>
  );
};

export default MobileResponsiveExample;
