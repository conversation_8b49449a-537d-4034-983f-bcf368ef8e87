import React from 'react';
import { UniversalChat } from '../UniversalChat';
import { Chat<PERSON><PERSON>ider } from '@/contexts/ChatContext';
import { Button } from '@/components/ui/button';
import { Smartphone, Monitor, CheckCircle } from 'lucide-react';

/**
 * Test component to verify mobile layout fixes
 */
export const MobileLayoutTest: React.FC = () => {
  return (
    <ChatProvider>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Header */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h1 className="text-3xl font-bold mb-4 flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
              Mobile Chat Layout - Fixed
            </h1>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h3 className="font-semibold text-green-700 flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  Mobile Fixes Applied
                </h3>
                <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
                  <li><strong>✅ Single view</strong> - Shows either conversation list OR chat content</li>
                  <li><strong>✅ Back button</strong> - Properly clears selected chat and returns to list</li>
                  <li><strong>✅ No participants panel</strong> - Removed as requested</li>
                  <li><strong>✅ Touch targets</strong> - 44px minimum for mobile interaction</li>
                  <li><strong>✅ Responsive layout</strong> - Adapts to screen size</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-semibold text-blue-700 flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  Desktop Layout
                </h3>
                <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
                  <li><strong>Split view</strong> - Sidebar and chat content side by side</li>
                  <li><strong>No participants panel</strong> - Removed as requested</li>
                  <li><strong>Compact design</strong> - Efficient use of screen space</li>
                  <li><strong>Multi-tasking</strong> - View conversations while chatting</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 className="font-semibold text-green-900 mb-2">How to test:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-green-800">
                <li>On mobile: Select a conversation → see chat content with back button</li>
                <li>Click back button → returns to conversation list</li>
                <li>Click participants button → see bottom drawer</li>
                <li>On desktop: See sidebar and chat side by side</li>
                <li>Click participants → see right-side sheet</li>
              </ol>
            </div>
          </div>

          {/* Live Chat Demo */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-4 border-b bg-gradient-to-r from-green-50 to-blue-50">
              <h2 className="text-xl font-semibold mb-2">Live Chat Demo</h2>
              <p className="text-gray-600 text-sm">
                Test the fixed mobile layout. On mobile, you'll see either the conversation list OR the chat content with a working back button.
              </p>
            </div>
            
            <div className="h-[600px]">
              <UniversalChat
                userRole="admin"
                userId="demo-admin"
                variant="full"
                theme="admin"
                onChatSelect={(chatId) => {
                  console.log('Selected chat:', chatId);
                }}
                onMessageSent={(message) => {
                  console.log('Sent message:', message);
                }}
                onError={(error) => {
                  console.error('Chat error:', error);
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </ChatProvider>
  );
};

export default MobileLayoutTest;
